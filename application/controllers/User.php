<?php defined('BASEPATH') OR exit('No direct script access allowed');

class User extends CI_Controller {
    
    public function __construct() {
        parent::__construct();
        $this->load->model('User_model', 'user_model');
    }
    
    public function index() {
        $this->load->view('user');
    }
    
    public function login() {
        // Utiliser en minuscules après le chargement
        $data['users'] = $this->user_model->getUsers();
        $this->load->view('user_list', $data);
    }
}
?>